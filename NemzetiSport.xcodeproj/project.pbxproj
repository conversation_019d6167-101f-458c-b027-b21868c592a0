// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		15295F9C4EB1CA6891956C17 /* Pods_NemzetiSport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6C3AA069A84CA86172D21F8C /* Pods_NemzetiSport.framework */; };
		321E4E571DC9C1E19CA2FADB /* .gitignore in Resources */ = {isa = PBXBuildFile; fileRef = 321E4C2E955D78304D81988D /* .gitignore */; };
		6CB02F7B23F2F79F0020E09C /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F7A23F2F79F0020E09C /* AppDelegate.swift */; };
		6CB02F7D23F2F79F0020E09C /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F7C23F2F79F0020E09C /* SceneDelegate.swift */; };
		6CB02F7F23F2F79F0020E09C /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F7E23F2F79F0020E09C /* ViewController.swift */; };
		6CB02F8423F2F7A20020E09C /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6CB02F8323F2F7A20020E09C /* Assets.xcassets */; };
		6CB02F8723F2F7A20020E09C /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6CB02F8523F2F7A20020E09C /* LaunchScreen.storyboard */; };
		6CB02F8F23F3DC960020E09C /* style.css in Resources */ = {isa = PBXBuildFile; fileRef = 6CB02F8E23F3DC960020E09C /* style.css */; };
		6CB02F9123F54B910020E09C /* script.js in Resources */ = {isa = PBXBuildFile; fileRef = 6CB02F9023F54B910020E09C /* script.js */; };
		6CB02F9323FBE4230020E09C /* Connection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F9223FBE4230020E09C /* Connection.swift */; };
		6CB02F9523FBFE7C0020E09C /* LaunchViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F9423FBFE7C0020E09C /* LaunchViewController.swift */; };
		6CB02F9723FC0CB30020E09C /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6CB02F9623FC0CB30020E09C /* Main.storyboard */; };
		6CB02F9B23FC0DC60020E09C /* OfflineViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB02F9A23FC0DC60020E09C /* OfflineViewController.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		6C2C7F7224A49D9300ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F6D24A49D9300ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = CF8230D8310EDE45CD6CAB3DC4D7DEA5;
			remoteInfo = "Pods-888";
		};
		6C2C7F7424A49D9300ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F6D24A49D9300ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 21B03CA622E690725A6626C088E1D09F;
			remoteInfo = ReachabilitySwift;
		};
		6C2C7F7C24A49F0700ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F7724A49F0700ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 078DD88D773AA138E4CF0B5A054ABBC5;
			remoteInfo = "Pods-NemzetiSport";
		};
		6C2C7F7E24A49F0700ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F7724A49F0700ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 21B03CA622E690725A6626C088E1D09F;
			remoteInfo = ReachabilitySwift;
		};
		6C2C7F8524A49F3500ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F8024A49F3500ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 078DD88D773AA138E4CF0B5A054ABBC5;
			remoteInfo = "Pods-NemzetiSport";
		};
		6C2C7F8724A49F3500ED29AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6C2C7F8024A49F3500ED29AB /* Pods.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 21B03CA622E690725A6626C088E1D09F;
			remoteInfo = ReachabilitySwift;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		6CB02FA823FD32120020E09C /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		321E4C2E955D78304D81988D /* .gitignore */ = {isa = PBXFileReference; lastKnownFileType = file.gitignore; path = .gitignore; sourceTree = "<group>"; };
		5966E5849BF99B66007C72CC /* Pods-NemzetiSport.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NemzetiSport.debug.xcconfig"; path = "Target Support Files/Pods-NemzetiSport/Pods-NemzetiSport.debug.xcconfig"; sourceTree = "<group>"; };
		6C2C7F6D24A49D9300ED29AB /* Pods.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Pods.xcodeproj; path = ../888/Pods/Pods.xcodeproj; sourceTree = "<group>"; };
		6C2C7F7724A49F0700ED29AB /* Pods.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Pods.xcodeproj; path = Pods/Pods.xcodeproj; sourceTree = "<group>"; };
		6C2C7F8024A49F3500ED29AB /* Pods.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Pods.xcodeproj; path = Pods/Pods.xcodeproj; sourceTree = "<group>"; };
		6C3AA069A84CA86172D21F8C /* Pods_NemzetiSport.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NemzetiSport.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6CB02F7723F2F79F0020E09C /* Nemzeti Sport.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Nemzeti Sport.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		6CB02F7A23F2F79F0020E09C /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		6CB02F7C23F2F79F0020E09C /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		6CB02F7E23F2F79F0020E09C /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		6CB02F8323F2F7A20020E09C /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		6CB02F8623F2F7A20020E09C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		6CB02F8823F2F7A20020E09C /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		6CB02F8E23F3DC960020E09C /* style.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; path = style.css; sourceTree = "<group>"; };
		6CB02F9023F54B910020E09C /* script.js */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.javascript; path = script.js; sourceTree = "<group>"; };
		6CB02F9223FBE4230020E09C /* Connection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Connection.swift; sourceTree = "<group>"; };
		6CB02F9423FBFE7C0020E09C /* LaunchViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaunchViewController.swift; sourceTree = "<group>"; };
		6CB02F9623FC0CB30020E09C /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Main.storyboard; sourceTree = "<group>"; };
		6CB02F9A23FC0DC60020E09C /* OfflineViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfflineViewController.swift; sourceTree = "<group>"; };
		9005E58B355ED6DC765D5626 /* Pods-NemzetiSport.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NemzetiSport.release.xcconfig"; path = "Target Support Files/Pods-NemzetiSport/Pods-NemzetiSport.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6CB02F7423F2F79F0020E09C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15295F9C4EB1CA6891956C17 /* Pods_NemzetiSport.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		58AB925E589B56D2D8629E15 /* Pods */ = {
			isa = PBXGroup;
			children = (
				5966E5849BF99B66007C72CC /* Pods-NemzetiSport.debug.xcconfig */,
				9005E58B355ED6DC765D5626 /* Pods-NemzetiSport.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6C2C7F6E24A49D9300ED29AB /* Products */ = {
			isa = PBXGroup;
			children = (
				6C2C7F7324A49D9300ED29AB /* Pods_888.framework */,
				6C2C7F7524A49D9300ED29AB /* Reachability.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6C2C7F7824A49F0700ED29AB /* Products */ = {
			isa = PBXGroup;
			children = (
				6C2C7F7D24A49F0700ED29AB /* Pods-NemzetiSport */,
				6C2C7F7F24A49F0700ED29AB /* Reachability.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6C2C7F8124A49F3500ED29AB /* Products */ = {
			isa = PBXGroup;
			children = (
				6C2C7F8624A49F3500ED29AB /* Pods-NemzetiSport */,
				6C2C7F8824A49F3500ED29AB /* Reachability.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6CB02F6E23F2F79F0020E09C = {
			isa = PBXGroup;
			children = (
				6CB02F7923F2F79F0020E09C /* NemzetiSport */,
				6CB02F7823F2F79F0020E09C /* Products */,
				58AB925E589B56D2D8629E15 /* Pods */,
				8F865F038069E12172B346B8 /* Frameworks */,
				6C2C7F7724A49F0700ED29AB /* Pods.xcodeproj */,
				321E4C2E955D78304D81988D /* .gitignore */,
			);
			sourceTree = "<group>";
		};
		6CB02F7823F2F79F0020E09C /* Products */ = {
			isa = PBXGroup;
			children = (
				6CB02F7723F2F79F0020E09C /* Nemzeti Sport.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6CB02F7923F2F79F0020E09C /* NemzetiSport */ = {
			isa = PBXGroup;
			children = (
				6CB02F9A23FC0DC60020E09C /* OfflineViewController.swift */,
				6CB02F9223FBE4230020E09C /* Connection.swift */,
				6CB02F7A23F2F79F0020E09C /* AppDelegate.swift */,
				6CB02F7E23F2F79F0020E09C /* ViewController.swift */,
				6CB02F9423FBFE7C0020E09C /* LaunchViewController.swift */,
				6CB02F9623FC0CB30020E09C /* Main.storyboard */,
				6CB02F8323F2F7A20020E09C /* Assets.xcassets */,
				6CB02F8523F2F7A20020E09C /* LaunchScreen.storyboard */,
				6CB02F8823F2F7A20020E09C /* Info.plist */,
				6CB02F8E23F3DC960020E09C /* style.css */,
				6CB02F9023F54B910020E09C /* script.js */,
				6CB02F7C23F2F79F0020E09C /* SceneDelegate.swift */,
			);
			path = NemzetiSport;
			sourceTree = "<group>";
		};
		8F865F038069E12172B346B8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6C2C7F6D24A49D9300ED29AB /* Pods.xcodeproj */,
				6C2C7F8024A49F3500ED29AB /* Pods.xcodeproj */,
				6C3AA069A84CA86172D21F8C /* Pods_NemzetiSport.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6CB02F7623F2F79F0020E09C /* NemzetiSport */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6CB02F8B23F2F7A20020E09C /* Build configuration list for PBXNativeTarget "NemzetiSport" */;
			buildPhases = (
				5C81988EE1620AB536950D2B /* [CP] Check Pods Manifest.lock */,
				6CB02F7323F2F79F0020E09C /* Sources */,
				6CB02F7423F2F79F0020E09C /* Frameworks */,
				6CB02F7523F2F79F0020E09C /* Resources */,
				6CB02FA823FD32120020E09C /* Embed Frameworks */,
				D22FCF0C3FED54F79EFE84CE /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NemzetiSport;
			productName = NemzetiSport;
			productReference = 6CB02F7723F2F79F0020E09C /* Nemzeti Sport.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6CB02F6F23F2F79F0020E09C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1130;
				LastUpgradeCheck = 1130;
				ORGANIZATIONNAME = "Demeter Gábor";
				TargetAttributes = {
					6CB02F7623F2F79F0020E09C = {
						CreatedOnToolsVersion = 11.3.1;
					};
				};
			};
			buildConfigurationList = 6CB02F7223F2F79F0020E09C /* Build configuration list for PBXProject "NemzetiSport" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6CB02F6E23F2F79F0020E09C;
			productRefGroup = 6CB02F7823F2F79F0020E09C /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 6C2C7F7824A49F0700ED29AB /* Products */;
					ProjectRef = 6C2C7F7724A49F0700ED29AB /* Pods.xcodeproj */;
				},
				{
					ProductGroup = 6C2C7F6E24A49D9300ED29AB /* Products */;
					ProjectRef = 6C2C7F6D24A49D9300ED29AB /* Pods.xcodeproj */;
				},
				{
					ProductGroup = 6C2C7F8124A49F3500ED29AB /* Products */;
					ProjectRef = 6C2C7F8024A49F3500ED29AB /* Pods.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				6CB02F7623F2F79F0020E09C /* NemzetiSport */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		6C2C7F7324A49D9300ED29AB /* Pods_888.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Pods_888.framework;
			remoteRef = 6C2C7F7224A49D9300ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		6C2C7F7524A49D9300ED29AB /* Reachability.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Reachability.framework;
			remoteRef = 6C2C7F7424A49D9300ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		6C2C7F7D24A49F0700ED29AB /* Pods-NemzetiSport */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			name = "Pods-NemzetiSport";
			path = Pods_NemzetiSport.framework;
			remoteRef = 6C2C7F7C24A49F0700ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		6C2C7F7F24A49F0700ED29AB /* Reachability.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Reachability.framework;
			remoteRef = 6C2C7F7E24A49F0700ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		6C2C7F8624A49F3500ED29AB /* Pods-NemzetiSport */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			name = "Pods-NemzetiSport";
			path = Pods_NemzetiSport.framework;
			remoteRef = 6C2C7F8524A49F3500ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		6C2C7F8824A49F3500ED29AB /* Reachability.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Reachability.framework;
			remoteRef = 6C2C7F8724A49F3500ED29AB /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		6CB02F7523F2F79F0020E09C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6CB02F9123F54B910020E09C /* script.js in Resources */,
				6CB02F8723F2F7A20020E09C /* LaunchScreen.storyboard in Resources */,
				6CB02F8F23F3DC960020E09C /* style.css in Resources */,
				6CB02F9723FC0CB30020E09C /* Main.storyboard in Resources */,
				6CB02F8423F2F7A20020E09C /* Assets.xcassets in Resources */,
				321E4E571DC9C1E19CA2FADB /* .gitignore in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		5C81988EE1620AB536950D2B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NemzetiSport-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D22FCF0C3FED54F79EFE84CE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NemzetiSport/Pods-NemzetiSport-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NemzetiSport/Pods-NemzetiSport-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NemzetiSport/Pods-NemzetiSport-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6CB02F7323F2F79F0020E09C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6CB02F7F23F2F79F0020E09C /* ViewController.swift in Sources */,
				6CB02F9B23FC0DC60020E09C /* OfflineViewController.swift in Sources */,
				6CB02F9523FBFE7C0020E09C /* LaunchViewController.swift in Sources */,
				6CB02F7B23F2F79F0020E09C /* AppDelegate.swift in Sources */,
				6CB02F7D23F2F79F0020E09C /* SceneDelegate.swift in Sources */,
				6CB02F9323FBE4230020E09C /* Connection.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		6CB02F8523F2F7A20020E09C /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				6CB02F8623F2F7A20020E09C /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6CB02F8923F2F7A20020E09C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		6CB02F8A23F2F7A20020E09C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6CB02F8C23F2F7A20020E09C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5966E5849BF99B66007C72CC /* Pods-NemzetiSport.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X8V5A3J543;
				INFOPLIST_FILE = NemzetiSport/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.1.6;
				PRODUCT_BUNDLE_IDENTIFIER = "hu.ringier.nso-test";
				PRODUCT_NAME = "Nemzeti Sport";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6CB02F8D23F2F7A20020E09C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9005E58B355ED6DC765D5626 /* Pods-NemzetiSport.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z7VCG248UV;
				INFOPLIST_FILE = NemzetiSport/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.1.6;
				PRODUCT_BUNDLE_IDENTIFIER = "hu.ringier.nso-test";
				PRODUCT_NAME = "Nemzeti Sport";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6CB02F7223F2F79F0020E09C /* Build configuration list for PBXProject "NemzetiSport" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6CB02F8923F2F7A20020E09C /* Debug */,
				6CB02F8A23F2F7A20020E09C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6CB02F8B23F2F7A20020E09C /* Build configuration list for PBXNativeTarget "NemzetiSport" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6CB02F8C23F2F7A20020E09C /* Debug */,
				6CB02F8D23F2F7A20020E09C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6CB02F6F23F2F79F0020E09C /* Project object */;
}
