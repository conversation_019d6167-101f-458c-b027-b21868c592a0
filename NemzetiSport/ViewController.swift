//
//  ViewController.swift
//  NemzetiSport
//
//  Created by <PERSON><PERSON> on 2020. 02. 11..
//  Copyright © 2020. Demeter Gábor. All rights reserved.
//

import UIKit
import WebKit
import UserNotifications

class ViewController: UIViewController, WK<PERSON>avigationDelegate, WKUIDelegate {
    
    @IBOutlet weak var webViewContainer: UIView!
    fileprivate var openUrl: URL?
    @IBOutlet weak var progressView: UIProgressView!
    @IBOutlet weak var progressLabel: UILabel!
    @IBOutlet weak var loadImage: UIImageView!
    @IBOutlet weak var loadingScreen: UIView!
    
    
    var webView: WKWebView!
    let config = WKWebViewConfiguration()
    let contentController = WKUserContentController()
    let network = NetworkManager.sharedInstance
    let progress = Progress(totalUnitCount: 100)
    
    var mEstProgress = 0

    deinit {
        self.webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.estimatedProgress))
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        network.reachability.whenUnreachable = { reachability in
            self.showOfflinePage()
        }
        
        loadUrl()
    }
    
    private func showOfflinePage() -> Void {
        DispatchQueue.main.async {
            self.performSegue(withIdentifier: "NetworkUnavailable", sender: self)
        }
    }
    
    func loadUrl() {
//        openUrl = URL(string: "http://m.nemzetisport.hu/?ao_teszt_tesztoldal=adocean")!
        openUrl = URL(string: "http://localhost:4200/")!
        // openUrl = URL(string: "http://www.nemzetisport.hu/")!
        guard let url = openUrl else { return }
        
        // add custom javascript
        if let script = generateAddScript() {
          contentController.addUserScript(script)
        }
        
        if let script = generateAddCSSScript() {
          contentController.addUserScript(script)
        }
        
        config.dataDetectorTypes = [.all]
        config.userContentController = contentController
        
        webView = WKWebView(frame: .zero, configuration: config)
        webView.navigationDelegate = self
        webView.allowsLinkPreview = true
        webView.uiDelegate = self
        webView.allowsBackForwardNavigationGestures = true
        webView.scrollView.bounces = false
        webView.addObserver(self, forKeyPath: #keyPath(WKWebView.estimatedProgress), options: .new, context: nil)
        webViewContainer.addSubview(webView)
        webViewContainer.sendSubviewToBack(webView)
        addConstraints(to: webView, with: webViewContainer)
        webView.load(NSURLRequest(url: url) as URLRequest)
    }
    
    func addConstraints(to webView: UIView, with superView: UIView) {
        webView.translatesAutoresizingMaskIntoConstraints = false
        let leadingConstraint = NSLayoutConstraint(item: webView, attribute: .leading, relatedBy: .equal, toItem: superView, attribute: .leading, multiplier: 1, constant: 0)
        let trailingConstraint = NSLayoutConstraint(item: webView, attribute: .trailing, relatedBy: .equal, toItem: superView, attribute: .trailing, multiplier: 1, constant: 0)
        let topConstraint = NSLayoutConstraint(item: webView, attribute: .top, relatedBy: .equal, toItem: superView, attribute: .top, multiplier: 1, constant: 0)
        let bottomConstraint = NSLayoutConstraint(item: webView, attribute: .bottom, relatedBy: .equal, toItem: superView, attribute: .bottom, multiplier: 1, constant: 0)
        superView.addConstraints([leadingConstraint, trailingConstraint, topConstraint, bottomConstraint])
    }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "estimatedProgress" {
            self.progressView.progress = 0.0
            self.progress.completedUnitCount = 0
            
            Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { (timer) in
                guard self.progress.isFinished == false else {
                    timer.invalidate()
                    return
                }
                
                let estProgress = Int(self.webView.estimatedProgress * 100)

                if (self.mEstProgress == estProgress) {
                    timer.invalidate()
                    self.hideLoadingScreen()
                    return
                }
                

                self.progress.completedUnitCount += 10
                self.progressView.setProgress(Float(self.progress.fractionCompleted), animated: true)
                self.progressLabel.text = "\(estProgress) %"
                
                self.mEstProgress = estProgress
            }
        }
    }
    
    func generateAddScript() -> WKUserScript? {
        // use "script" file name for custom javascript
        guard let scriptPath = Bundle.main.path(forResource: "script", ofType: "js"),
              let scriptSource = try? String(contentsOfFile: scriptPath) else {
            return nil
        }
      
        let script = WKUserScript(source: scriptSource, injectionTime: .atDocumentStart, forMainFrameOnly: true)
      
        return script
    }
    
    func generateAddCSSScript() -> WKUserScript? {
        guard let cssPath = Bundle.main.path(forResource: "style", ofType: "css"),
        let cssString = try? String(contentsOfFile: cssPath).components(separatedBy: .newlines).joined() else {
        return nil
        }

        let source = """
        var style = document.createElement('style');
        style.innerHTML = '\(cssString)';
        document.head.appendChild(style);
        """

        let script = WKUserScript(source: source, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        return script
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void)
    {
        if navigationAction.navigationType == WKNavigationType.linkActivated {
            if navigationAction.request.url?.scheme == "mailto" {
                UIApplication.shared.open(navigationAction.request.url!)
                decisionHandler(.cancel)
                return
            }
            
            if let host = navigationAction.request.url?.host {
                if host.contains("www.nemzetisport.hu") {
                    if navigationAction.targetFrame == nil {
                        webView.load(navigationAction.request)
                    }
                    decisionHandler(.allow)

//                    // AdOcean test:
//                    var components = URLComponents()
//                    components.scheme = navigationAction.request.url?.scheme
//                    components.host =  navigationAction.request.url?.host
//                    components.path =  navigationAction.request.url!.path
//                    components.query = "ao_teszt_tesztoldal=adocean"
//                    let customRequest = URLRequest(url: components.url!)
//
//                    webView.load(customRequest)
//                    decisionHandler(.cancel)

                    return
                } else if host.contains("m.facebook.com") {
                    openExternalLink(navigationAction.request.url!)
                } else if host.contains("www.google.com") {
                    if navigationAction.targetFrame == nil {
                        webView.load(navigationAction.request)
                    }
                    decisionHandler(.allow)
                    return
                } else {
                    let urlString = navigationAction.request.url?.absoluteString ?? ""
                    let url = URL(string: urlString)
                    openExternalLink(url!)
                }
            }

            decisionHandler(.cancel)
            return
        }
        decisionHandler(WKNavigationActionPolicy.allow)
    }
    
    func openExternalLink(_ url: URL) {
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url)
        }
    }
    
    /**
        Use credentials for basic auth
        Use it only on the development site.
        Only works for the main page, for the first url load
     */
//    func webView(_ webView: WKWebView, didReceive challenge: URLAuthenticationChallenge,
//                 completionHandler: @escaping(URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
//        let credential = URLCredential(user: "replace...",
//                                       password: "replace...",
//                                       persistence: .forSession)
//        completionHandler(.useCredential, credential)
//    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        self.hideLoadingScreen()
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        self.hideLoadingScreen()
    }
    
    func hideLoadingScreen() {
        UIView.animate(withDuration: 1) {
            self.loadingScreen.alpha = 0
        }
    }
    
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
    }
}
