<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="NYr-GN-eiQ">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Offline View Controller-->
        <scene sceneID="Kfq-AO-mKc">
            <objects>
                <viewController modalPresentationStyle="overFullScreen" id="LfA-Lb-gjV" customClass="OfflineViewController" customModule="Nemzeti_Sport" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cnU-86-uga">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bJr-xb-OiY">
                                <rect key="frame" x="67.5" y="255" width="240" height="157"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="offline" translatesAutoresizingMaskIntoConstraints="NO" id="5C4-Ff-EhK">
                                        <rect key="frame" x="45" y="0.0" width="150" height="145"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="150" id="X19-mM-nLk"/>
                                            <constraint firstAttribute="height" constant="145" id="Xnc-hv-FTV"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You are offline, connect to the internet" textAlignment="center" lineBreakMode="clip" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Nwc-13-YO2">
                                        <rect key="frame" x="0.0" y="123" width="240" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="4Cw-46-cS4"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                        <color key="textColor" red="0.51433295759999997" green="0.54734961000000004" blue="0.56930343380000004" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <constraints>
                                    <constraint firstItem="Nwc-13-YO2" firstAttribute="centerX" secondItem="5C4-Ff-EhK" secondAttribute="centerX" id="5zg-EI-E1i"/>
                                    <constraint firstAttribute="height" constant="157" id="6Fc-2V-GLs"/>
                                    <constraint firstAttribute="width" constant="240" id="B8A-AH-WBb"/>
                                    <constraint firstAttribute="bottom" secondItem="Nwc-13-YO2" secondAttribute="bottom" id="CDJ-5t-cqG"/>
                                    <constraint firstItem="Nwc-13-YO2" firstAttribute="leading" secondItem="bJr-xb-OiY" secondAttribute="leading" id="NVu-a5-LNE"/>
                                    <constraint firstItem="5C4-Ff-EhK" firstAttribute="leading" secondItem="bJr-xb-OiY" secondAttribute="leading" constant="45" id="bir-XL-k7G"/>
                                    <constraint firstAttribute="trailing" secondItem="5C4-Ff-EhK" secondAttribute="trailing" constant="45" id="lKX-za-V7u"/>
                                    <constraint firstItem="5C4-Ff-EhK" firstAttribute="centerX" secondItem="bJr-xb-OiY" secondAttribute="centerX" id="omP-GV-2XR"/>
                                    <constraint firstItem="5C4-Ff-EhK" firstAttribute="top" secondItem="bJr-xb-OiY" secondAttribute="top" id="zp3-h3-guf"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="bJr-xb-OiY" firstAttribute="centerX" secondItem="cnU-86-uga" secondAttribute="centerX" id="Yl5-69-9rW"/>
                            <constraint firstItem="bJr-xb-OiY" firstAttribute="centerY" secondItem="cnU-86-uga" secondAttribute="centerY" id="sId-jV-w4J"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="kip-Xa-zoE"/>
                    </view>
                    <navigationItem key="navigationItem" id="gEN-Nv-aay"/>
                    <connections>
                        <segue destination="bvc-or-eMn" kind="showDetail" identifier="MainController" id="4aT-Uh-LSs"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tMV-E2-1Z0" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="680.79999999999995" y="-156.07196401799101"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="GbB-kx-16P">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" modalPresentationStyle="fullScreen" navigationBarHidden="YES" id="bvc-or-eMn" sceneMemberID="viewController">
                    <navigationItem key="navigationItem" id="WRl-6M-rJc"/>
                    <nil key="simulatedBottomBarMetrics"/>
                    <splitViewMasterSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="E0U-xA-lA3">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="LsC-1q-a6k" kind="relationship" relationship="rootViewController" id="5BW-5s-gHM"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="KWy-7u-iKG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="681" y="591"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="Waj-jY-Spn">
            <objects>
                <viewController id="LsC-1q-a6k" customClass="ViewController" customModule="Nemzeti_Sport" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="DYa-wr-TiC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cBl-tS-L0B">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xSf-PH-2OH">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 %" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="neY-16-LG6">
                                        <rect key="frame" x="173" y="604.5" width="29" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" progress="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="1vt-Pg-Jfq">
                                        <rect key="frame" x="44" y="645" width="287" height="2"/>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </progressView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="512" translatesAutoresizingMaskIntoConstraints="NO" id="kaa-Ya-FDb">
                                        <rect key="frame" x="59.5" y="205.5" width="256" height="256"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="256" id="0XH-iO-D0v"/>
                                            <constraint firstAttribute="width" constant="256" id="bGA-dz-h14"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="1vt-Pg-Jfq" firstAttribute="leading" secondItem="xSf-PH-2OH" secondAttribute="leading" constant="44" id="6KX-1c-O97"/>
                                    <constraint firstItem="kaa-Ya-FDb" firstAttribute="centerY" secondItem="xSf-PH-2OH" secondAttribute="centerY" id="CVt-Y5-jro"/>
                                    <constraint firstItem="1vt-Pg-Jfq" firstAttribute="top" secondItem="neY-16-LG6" secondAttribute="bottom" constant="20" id="Cks-az-iV6"/>
                                    <constraint firstAttribute="bottom" secondItem="1vt-Pg-Jfq" secondAttribute="bottom" constant="20" id="GAt-6S-JFh"/>
                                    <constraint firstItem="1vt-Pg-Jfq" firstAttribute="centerX" secondItem="xSf-PH-2OH" secondAttribute="centerX" id="ILC-gb-MXD"/>
                                    <constraint firstItem="kaa-Ya-FDb" firstAttribute="centerX" secondItem="xSf-PH-2OH" secondAttribute="centerX" id="LHW-LY-e3M"/>
                                    <constraint firstItem="1vt-Pg-Jfq" firstAttribute="top" secondItem="neY-16-LG6" secondAttribute="bottom" constant="20" id="rvW-pG-ScL"/>
                                    <constraint firstItem="neY-16-LG6" firstAttribute="centerX" secondItem="xSf-PH-2OH" secondAttribute="centerX" id="sic-zT-1uD"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="xSf-PH-2OH" firstAttribute="centerX" secondItem="cBl-tS-L0B" secondAttribute="centerX" id="9tz-Cu-kxn"/>
                            <constraint firstItem="xSf-PH-2OH" firstAttribute="bottom" secondItem="DYa-wr-TiC" secondAttribute="bottom" id="BUx-Uj-y7g"/>
                            <constraint firstItem="xSf-PH-2OH" firstAttribute="leading" secondItem="cBl-tS-L0B" secondAttribute="leading" id="Hiv-o5-Qt3"/>
                            <constraint firstItem="cBl-tS-L0B" firstAttribute="top" secondItem="3yN-SP-DSf" secondAttribute="top" id="N0t-1A-Ioj"/>
                            <constraint firstItem="cBl-tS-L0B" firstAttribute="leading" secondItem="3yN-SP-DSf" secondAttribute="leading" id="Obb-MZ-n3h"/>
                            <constraint firstItem="xSf-PH-2OH" firstAttribute="top" secondItem="DYa-wr-TiC" secondAttribute="top" id="Phq-JE-lSq"/>
                            <constraint firstAttribute="bottom" secondItem="cBl-tS-L0B" secondAttribute="bottom" id="Qgd-S6-aJF"/>
                            <constraint firstItem="cBl-tS-L0B" firstAttribute="trailing" secondItem="3yN-SP-DSf" secondAttribute="trailing" id="u87-B1-gHG"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="3yN-SP-DSf"/>
                    </view>
                    <navigationItem key="navigationItem" id="LNP-WQ-rcv"/>
                    <connections>
                        <outlet property="loadImage" destination="kaa-Ya-FDb" id="AuS-kT-mO1"/>
                        <outlet property="loadingScreen" destination="xSf-PH-2OH" id="gwt-iA-bRR"/>
                        <outlet property="progressLabel" destination="neY-16-LG6" id="GzW-pE-zc6"/>
                        <outlet property="progressView" destination="1vt-Pg-Jfq" id="J9b-aH-VP7"/>
                        <outlet property="webViewContainer" destination="cBl-tS-L0B" id="upe-UQ-CGg"/>
                        <segue destination="LfA-Lb-gjV" kind="showDetail" identifier="NetworkUnavailable" id="Kj1-3q-QVI"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8zq-Va-ZCe" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1525.5999999999999" y="590.55472263868069"/>
        </scene>
        <!--Launch View Controller-->
        <scene sceneID="3qN-vA-oll">
            <objects>
                <viewController id="NYr-GN-eiQ" customClass="LaunchViewController" customModule="Nemzeti_Sport" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="QsH-50-l3a">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="512" translatesAutoresizingMaskIntoConstraints="NO" id="cy7-m0-Gww">
                                <rect key="frame" x="59.5" y="205.5" width="256" height="256"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="256" id="FHJ-v7-fUZ"/>
                                    <constraint firstAttribute="height" constant="256" id="bkA-KB-rqw"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="cy7-m0-Gww" firstAttribute="centerY" secondItem="QsH-50-l3a" secondAttribute="centerY" id="OWR-3y-5Fc"/>
                            <constraint firstItem="cy7-m0-Gww" firstAttribute="centerX" secondItem="QsH-50-l3a" secondAttribute="centerX" id="vdw-DL-Qe3"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="HoY-Gt-JTF"/>
                    </view>
                    <connections>
                        <segue destination="LfA-Lb-gjV" kind="showDetail" identifier="NetworkUnavailable" id="Tqt-db-y7j"/>
                        <segue destination="bvc-or-eMn" kind="show" identifier="MainController" animates="NO" id="OB7-rs-Xrj"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="TES-O4-fEn" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-50.399999999999999" y="208.24587706146929"/>
        </scene>
    </scenes>
    <resources>
        <image name="512" width="512" height="512"/>
        <image name="offline" width="150" height="145"/>
    </resources>
    <inferredMetricsTieBreakers>
        <segue reference="OB7-rs-Xrj"/>
        <segue reference="Kj1-3q-QVI"/>
    </inferredMetricsTieBreakers>
</document>
