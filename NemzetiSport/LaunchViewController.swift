//
//  LaunchViewController.swift
//  NemzetiSport
//
//  Created by <PERSON><PERSON> G<PERSON> on 2020. 02. 18..
//  Copyright © 2020. Demeter Gábor. All rights reserved.
//

import UIKit

class LaunchViewController: UIViewController {
    let network: NetworkManager = NetworkManager.sharedInstance
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        NetworkManager.isUnreachable { _ in
            self.showOfflinePage()
        }
        
        NetworkManager.isReachable { _ in
            self.showMainPage()
        }
    }
    
    private func showOfflinePage() -> Void {
        DispatchQueue.main.async {
            self.performSegue(withIdentifier: "NetworkUnavailable", sender: self)
        }
    }
    
    private func showMainPage() -> Void {
        DispatchQueue.main.async {
            self.performSegue(withIdentifier: "MainController", sender: self)
        }
    }
}

