<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NemzetiSport.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>6C6F661ED809BE5FA99604A2E3D48F7DB299D174</string>
				<key>dateExpires</key>
				<string>2021. 02. 20.</string>
				<key>type</key>
				<string>Apple Distribution</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>6C6F661ED809BE5FA99604A2E3D48F7DB299D174</string>
						<key>dateExpires</key>
						<string>2021. 02. 20.</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>Reachability.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>Z7VCG248UV</string>
						<key>name</key>
						<string>Mediaworks Kiado es Nyomda Kft.</string>
					</dict>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>6SF8JLEWLW.hu.ringier.nso</string>
				<key>com.apple.developer.team-identifier</key>
				<string>Z7VCG248UV</string>
				<key>get-task-allow</key>
				<false/>
				<key>keychain-access-groups</key>
				<array>
					<string>6SF8JLEWLW.hu.ringier.nso</string>
				</array>
			</dict>
			<key>name</key>
			<string>Nemzeti Sport.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>a8f89d60-864f-487f-85ec-c2548960d67f</string>
				<key>name</key>
				<string>iOS Team Ad Hoc Provisioning Profile: hu.ringier.nso</string>
			</dict>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>Z7VCG248UV</string>
				<key>name</key>
				<string>Mediaworks Kiado es Nyomda Kft.</string>
			</dict>
		</dict>
	</array>
</dict>
</plist>
