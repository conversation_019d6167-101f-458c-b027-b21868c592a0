//
//  AppDelegate.swift
//  NemzetiSport
//
//  Created by <PERSON><PERSON> Gábor on 2020. 02. 11..
//  Copyright © 2020. Demeter Gábor. All rights reserved.
//

import UIKit
import AppTrackingTransparency

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        return true
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        requestTrackingPermission()
    }

    func requestTrackingPermission() {
        if #available(iOS 14, *) {
            ATTrackingManager.requestTrackingAuthorization { status in
//                switch status {
//                    case .notDetermined:
//                        print("notDetermined tracking")
//                    case .restricted:
//                        print("restricted tracking")
//                    case .authorized:
//                        print("enable tracking")
//                    case .denied:
//                        print("disable tracking - denied")
//                    default:
//                        print("disable tracking - default")
//                }
            }
        }
    }
}

