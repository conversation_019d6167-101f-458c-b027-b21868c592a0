//
//  CacheService.swift
//  NemzetiSport
//
//  Created by De<PERSON> Gábor on 2020. 02. 19..
//  Copyright © 2020. Demeter Gábor. All rights reserved.
//

import Foundation
import UIKit

class ImageService {
    static let cache = NSCache<NSString, UIImage>()
    
    static func downloadImage(withURL url:URL, completion: @escaping (_ image:UIImage?)->()) {
        let dataTask = URLSession.shared.dataTask(with: url) {data, responseUrl, error in
            var downloadImage:UIImage?
            
            if let data = data {
                downloadImage = UIImage(data: data)
            }
            
            if downloadImage != nil {
                cache.setObject(downloadImage!, forKey: url.absoluteString as NSString)
            }
            
            DispatchQueue.main.async {
                completion(downloadImage)
            }
        }
        
        print("jani")
        print(dataTask.resume())
        print("jani")
        
        dataTask.resume()
    }
    
    static func getImage(withURL url:URL, completion: @escaping (_ image:UIImage?)->()) {
        if let image = cache.object(forKey: url.absoluteString as NSString) {
            print("benne van")
            completion(image)
        } else {
            print("nincs benne")
            downloadImage(withURL: url, completion: completion)
        }
    }
}
