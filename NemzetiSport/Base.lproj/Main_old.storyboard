<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="Nemzeti_Sport" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="geZ-C3-t7e">
                                <rect key="frame" x="0.0" y="44" width="414" height="852"/>
                                <subviews>
                                    <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" style="medium" translatesAutoresizingMaskIntoConstraints="NO" id="Bov-Xw-o6t">
                                        <rect key="frame" x="197" y="416" width="20" height="20"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </activityIndicatorView>
                                </subviews>
                                <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="Bov-Xw-o6t" firstAttribute="centerY" secondItem="geZ-C3-t7e" secondAttribute="centerY" id="NDC-Km-5Lt"/>
                                    <constraint firstItem="Bov-Xw-o6t" firstAttribute="centerX" secondItem="geZ-C3-t7e" secondAttribute="centerX" id="g0t-c5-o4D"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" red="0.54543608430000001" green="0.0045277313330000001" blue="0.0055068070070000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="geZ-C3-t7e" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="Hbt-CZ-o1V"/>
                            <constraint firstAttribute="bottom" secondItem="geZ-C3-t7e" secondAttribute="bottom" id="Nna-oH-o8f"/>
                            <constraint firstItem="geZ-C3-t7e" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="lGU-Sb-ade"/>
                            <constraint firstItem="geZ-C3-t7e" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="uMX-qn-xkX"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                    <connections>
                        <outlet property="Activity" destination="Bov-Xw-o6t" id="AS5-lu-tMy"/>
                        <outlet property="webViewContainer" destination="geZ-C3-t7e" id="BaZ-ei-1q9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="138.61607142857142"/>
        </scene>
    </scenes>
</document>
