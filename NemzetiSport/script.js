const nsoIosApp_ZoneDivIdPairs = new Map();
// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508040", "zone_mobile_7273867");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuuklphjlmkg", "ado-iV1H4iF8h2bSgyZYCUtTeYLvsLp604LktvxjxfJ9pzb.N7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508045", "zone_mobile_7273872");
nsoIosApp_ZoneDivIdPairs.set("adoceanhurlcglwerfb", "ado-bEyakJAlo2KQ3J2SK8RWDLDBTGunsB.dU05s1nr8HtD.o7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508049", "zone_mobile_7273878");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuomjmotofoa", "ado-noWgAeZzlxiiwevdSav6vWehsNjAToLeXWZjiZYaCUT.p7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508050", "zone_mobile_7273880");
nsoIosApp_ZoneDivIdPairs.set("adoceanhulnqsbrikjv", "ado-xv_XEHrXNzXliaX48gqzBAb3DXzQ6qe0OkV42gQvne3.a7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508053", "zone_mobile_7273887");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuynhjfosotq", "ado-1jgFdeMK7QcKZJWx.tFSNzcBjK8gXZAb2fPPG.33v9X.H7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6508055", "zone_mobile_7273888");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuvoopilmtdl", "ado-mn2nFVR_l2F_wNEWiMlXMviuwHdY7SL_.yDznZ4UV0L.Y7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273534", "zone_mobile_7273891");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuspfgmigitk", "ado-t8kxibrZXJ0.b5djgRZzECULASH9qgdmwqjdLav_qP3.X7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273538", "zone_mobile_7273894");
nsoIosApp_ZoneDivIdPairs.set("adoceanhupanmpvpmsf", "ado-kdQFCE9K7VIKC4U_84LitGgGroCYDxuAP92rZl6D9bP..7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273539", "zone_mobile_7273903");
nsoIosApp_ZoneDivIdPairs.set("adoceanhumbetctjrya", "ado-85VcMBonnXZmgqlMafCBgBLx4m_K6t3fVMYAmiAS9oL.f7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273540", "zone_mobile_7273904");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuzbljgqdgsz", "ado-JOg2aAz4gtkCJ1TaZOUuYRj9o_MA55Jx34Am6G0PiIr.57");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273542", "zone_mobile_7273905");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuwccqjnnkcu", "ado-RbzsfSCILThu8Q9DuJUOK9i.EzaIu.IECKsfLvGbU1r.o7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273545", "zone_mobile_7273914");
nsoIosApp_ZoneDivIdPairs.set("adoceanhutdjgnkhpxp", "ado-KPz1WkFmja4N2h7.Zi2_uO.w.Jr.T..kXAVJnhMBoNv.K7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273546", "zone_mobile_7273915");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuqeqmqxqtlk", "ado-SFRKWo4187eAEmhznR8gOZDAL65XLU8tXNrEV.6cXMz.Q7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7273547", "zone_mobile_7273916");
nsoIosApp_ZoneDivIdPairs.set("adoceanhunfhtdvkibj", "ado-AfHx8KePDAyLkMeZ340jfQZiIs21al4O1D.AreRSBlL.p7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6380511", "zone_mobile_7273917");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuzfofqninol", "ado-AfHx8KePDAyLkMeZ340jfQZiIs21al4O1D.AreRSBlL.p7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6376965", "zone_mobile_7273918");
nsoIosApp_ZoneDivIdPairs.set("adoceanhupeqijteukr", "ado-vP2rG.NMqCCrpokmVVlpfvDroZRH1EcgYynKRCrVe6f.M7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_4637493", "zone_mobile_7273934");
nsoIosApp_ZoneDivIdPairs.set("adoceanhumfhpmqoieq", "ado-2PVF2kDdPZRXqfoUVUDY34MXo956fJIDnmsW0_U0aoD.Z7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_1687721", "zone_mobile_7273938");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuwgfmdlsrjg", "ado-c0tMPbnsPbmW144t2oqhtVLJA3gq_VKklD75ympV5Dz.87");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_2882490", "zone_mobile_7273941");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuthmsgimgdf", "ado-9efqP.Bc412fJHJyROZ3ttRTz2PCno7c38mUea88bCD.m7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_2938546", "zone_mobile_7273948");
nsoIosApp_ZoneDivIdPairs.set("adoceanhuqidjkvflya", "ado-_5GgHKb2l17P2zc3hdGaCQ.2coF.rn6l1ijjCxlKw.z.y7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_6234384", "zone_mobile_7273955");
nsoIosApp_ZoneDivIdPairs.set("adoceanhunjkpnsppxv", "ado-8TSWMS_wkiDPF.APDKEdS0W.XQ19hcdDXG8I3kDSbwz.A7");

// nsoIosApp_ZoneDivIdPairs.set("zone_mobile_7275027", "zone_mobile_7275028"); // ez nincs meg

// Find all element on page / in parent
var nsoIosApp_FindAllElements = function (selector, parent) {
    return Array.prototype.slice.call((parent ? parent : document).querySelectorAll(selector));
};

// Insert element after the existing one
var nsoIosApp_InsertAfter = function (newElement, referenceElement) {
    referenceElement.parentNode.insertBefore(newElement, referenceElement.nextSibling);
}

// Remove element
var nsoIosApp_RemoveElement = function (elem) {
    elem.remove();
}

// const replacementSelector = ".goAdverticum";
// const replacementClass = "goAdverticum";
const replacementSelector = ".adOceanMobile";
const replacementClass = "adOceanMobile";

// Ad zone replacement
// Create new zone div element with the given zoneId
var nsoIosApp_CreateZoneDivElement = function (zoneId) {
    let zoneDiv = document.createElement('div');
    zoneDiv.id = zoneId;
    zoneDiv.className = replacementClass;
    zoneDiv.style = "display:inline-block;";

    return zoneDiv;
}

// create script element for the zone div (adocean)
var nsoIosApp_CreateZoneScriptElement = function (zoneId) {
    let script = document.createElement('script');
    script.type = "text/javascript"
    script.text = "ado.placement({id: '" + zoneId + "', server: 'hu.adocean.pl' });"

    return script;
}

// Replace each zone div with the given pair
var nsoIosApp_DoZoneReplacement = function () {
    var zones = nsoIosApp_FindAllElements(replacementSelector);

    zones.forEach((elem) => {
        var newZoneId = nsoIosApp_ZoneDivIdPairs.get(elem.id);

        if (typeof newZoneId !== "undefined") {
            nsoIosApp_InsertAfter(nsoIosApp_CreateZoneScriptElement(newZoneId), elem); // for AdOcean
            nsoIosApp_InsertAfter(nsoIosApp_CreateZoneDivElement(newZoneId), elem);
            nsoIosApp_RemoveElement(elem);
        }
    });
}

// Handle sponsored menu style
var nsoIosApp_StyleSponsoredMenu = function () {
    var menuItems = nsoIosApp_FindAllElements('.menu > ul > li');
    menuItems.forEach((elem) => {
        elem.style = "background-color: " + elem.style.backgroundColor + "!important";

        let menuTextDivs = nsoIosApp_FindAllElements('div.text', elem);
        menuTextDivs.forEach((div) => {
            div.style = "color: " + div.style.color + "!important";
        })
    });
}


// When DOM loaded run the zone replacement
var documentLoaded = false;
document.onreadystatechange = function () {
    if (!documentLoaded && document.readyState === "interactive") {
        // Initialize your application or run some code.
        documentLoaded = true;
        
        // Replace zone divs
        nsoIosApp_DoZoneReplacement();
        
        // Replace sponsored menu css
        nsoIosApp_StyleSponsoredMenu();
    }
}
